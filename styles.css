:root {
  /* 主色调 */
  --primary: #8C52FF;
  --primary-dark: #6E3AD9;
  --primary-light: #A875FF;
  
  /* 霓虹色调 */
  --neon-pink: #FF2E93;
  --neon-blue: #00E9FF;
  --neon-green: #00FF85;
  --neon-yellow: #FFDE59;
  
  /* 暗色背景 */
  --bg-dark: #121212;
  --bg-card: #1E1E1E;
  --bg-card-hover: #252525;
  
  /* 文本颜色 */
  --text-primary: #FFFFFF;
  --text-secondary: rgba(255, 255, 255, 0.7);
  --text-tertiary: rgba(255, 255, 255, 0.5);
  --text-disabled: rgba(255, 255, 255, 0.3);
  
  /* 边框与阴影 */
  --border-light: rgba(255, 255, 255, 0.1);
  --shadow-card: 0 4px 20px rgba(0, 0, 0, 0.25);
  --shadow-neon: 0 0 15px rgba(140, 82, 255, 0.5);
  
  /* 间距 */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  
  /* 圆角 */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 20px;
  --radius-full: 9999px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Outfit', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
}

body {
  background-color: var(--bg-dark);
  color: var(--text-primary);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  padding: var(--space-md);
}

.app-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xl);
  justify-content: center;
  padding-bottom: var(--space-xl);
}

.screen {
  width: 360px;
  height: 720px;
  background: var(--bg-dark);
  border-radius: var(--radius-lg);
  overflow: hidden;
  position: relative;
  box-shadow: var(--shadow-card);
  border: 1px solid var(--border-light);
  display: none;
}

.screen:target,
.screen#splash,
.screen#login {
  display: block;
}

.screen#splash:not(:target) {
  display: block;
}

.header {
  padding: var(--space-md) var(--space-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 10;
}

.header-title {
  font-size: 20px;
  font-weight: 600;
}

.header-action {
  display: flex;
  gap: var(--space-sm);
}

.icon-button {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.icon-button:hover, .icon-button:active {
  background: rgba(255, 255, 255, 0.2);
}

.content {
  height: calc(100% - 160px);
  overflow-y: auto;
  padding: 0 var(--space-md) var(--space-md);
}

.full-content {
  height: calc(100% - 80px);
  overflow-y: auto;
  padding: 0 var(--space-md) var(--space-md);
}

.nav-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
  background: var(--bg-card);
  display: flex;
  justify-content: space-around;
  align-items: center;
  border-top: 1px solid var(--border-light);
  padding: 0 var(--space-md);
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-tertiary);
  text-decoration: none;
  font-size: 12px;
}

.nav-item.active {
  color: var(--primary);
}

.nav-icon {
  font-size: 24px;
  margin-bottom: var(--space-xs);
}

.card {
  background: var(--bg-card);
  border-radius: var(--radius-md);
  padding: var(--space-md);
  margin-bottom: var(--space-md);
  border: 1px solid var(--border-light);
  transition: all 0.2s ease;
}

.card:active {
  transform: scale(0.98);
  background: var(--bg-card-hover);
}

.feast-card {
  background: var(--bg-card);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-md);
  overflow: hidden;
  position: relative;
  box-shadow: var(--shadow-card);
  text-decoration: none;
  color: inherit;
  display: block;
  transition: all 0.3s ease;
  animation: fadeInUp 0.6s ease-out;
}

.feast-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
}

.feast-card:nth-child(2) {
  animation-delay: 0.1s;
}

.feast-card:nth-child(3) {
  animation-delay: 0.2s;
}

.feast-card:nth-child(4) {
  animation-delay: 0.3s;
}

.feast-image {
  height: 150px;
  position: relative;
  overflow: hidden;
}

.feast-image-bg {
  width: 100%;
  height: 100%;
  background-position: center;
  background-size: cover;
  filter: brightness(0.7);
}

.feast-details {
  padding: var(--space-md);
}

.feast-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-sm);
}

.feast-date {
  font-size: 14px;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
}

.feast-icon {
  margin-right: var(--space-xs);
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
  margin: var(--space-sm) 0;
}

.tag {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-secondary);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: 12px;
}

.tag.highlighted {
  background: var(--primary);
  color: white;
}

.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--radius-full);
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.button.primary {
  background: var(--primary);
  color: white;
}

.button.primary:hover, .button.primary:active {
  background: var(--primary-dark);
}

.button.outline {
  background: transparent;
  border: 1px solid var(--primary);
  color: var(--primary);
}

.button.outline:hover, .button.outline:active {
  background: rgba(140, 82, 255, 0.1);
}

.button.full {
  width: 100%;
}

.button-icon {
  margin-right: var(--space-sm);
}

.gradient-text {
  background: linear-gradient(to right, var(--neon-pink), var(--primary));
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  font-weight: 700;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  margin: var(--space-lg) 0 var(--space-md);
  display: flex;
  align-items: center;
}

.avatar {
  width: 50px;
  height: 50px;
  border-radius: var(--radius-full);
  object-fit: cover;
}

.avatar.small {
  width: 36px;
  height: 36px;
}

.avatar-group {
  display: flex;
  margin-left: var(--space-sm);
}

.avatar-group .avatar {
  margin-left: -10px;
  border: 2px solid var(--bg-card);
}

.avatar-group .avatar:first-child {
  margin-left: 0;
}

.users-more {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-full);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-left: -10px;
  border: 2px solid var(--bg-card);
}

.search-bar {
  display: flex;
  align-items: center;
  background: var(--bg-card);
  border-radius: var(--radius-full);
  padding: var(--space-sm) var(--space-md);
  margin-bottom: var(--space-md);
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
  position: relative;
}

.search-bar:hover {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(140, 82, 255, 0.1);
  transform: translateY(-1px);
}

.search-bar:focus-within {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(140, 82, 255, 0.2);
}

.search-icon {
  color: var(--text-tertiary);
  margin-right: var(--space-sm);
}

.search-input {
  background: transparent;
  border: none;
  color: var(--text-primary);
  flex: 1;
  font-size: 16px;
}

.search-input:focus {
  outline: none;
}

.categories {
  display: flex;
  overflow-x: auto;
  gap: var(--space-sm);
  padding: var(--space-xs) 0;
  margin-bottom: var(--space-md);
  scrollbar-width: none;
}

.categories::-webkit-scrollbar {
  display: none;
}

.category-item {
  padding: var(--space-sm) var(--space-md);
  background: var(--bg-card);
  border-radius: var(--radius-full);
  white-space: nowrap;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.category-item:hover {
  background: var(--bg-card-hover);
  border-color: var(--primary);
  transform: translateY(-1px);
}

.category-item.active {
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  color: white;
  box-shadow: 0 4px 15px rgba(140, 82, 255, 0.3);
}

.category-item.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  animation: shimmer 2s infinite;
}

.form-group {
  margin-bottom: var(--space-lg);
}

.form-label {
  display: block;
  margin-bottom: var(--space-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: var(--space-md);
  background: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: 16px;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary);
}

textarea.form-input {
  min-height: 100px;
  resize: vertical;
}

.form-select {
  width: 100%;
  padding: var(--space-md);
  background: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: 16px;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white' width='18px' height='18px'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right var(--space-md) center;
}

.form-select:focus {
  outline: none;
  border-color: var(--primary);
}

.login-logo {
  font-size: 36px;
  font-weight: 800;
  text-align: center;
  margin: var(--space-xl) 0;
}

.social-login {
  display: flex;
  justify-content: center;
  gap: var(--space-md);
  margin: var(--space-xl) 0;
}

.social-icon {
  width: 50px;
  height: 50px;
  background: var(--bg-card);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: var(--text-primary);
  text-decoration: none;
}

.divider {
  display: flex;
  align-items: center;
  color: var(--text-tertiary);
  margin: var(--space-xl) 0;
}

.divider:before, .divider:after {
  content: "";
  flex: 1;
  height: 1px;
  background: var(--border-light);
}

.divider:before {
  margin-right: var(--space-md);
}

.divider:after {
  margin-left: var(--space-md);
}

.create-feast-fab {
  position: absolute;
  bottom: 100px;
  right: var(--space-md);
  width: 60px;
  height: 60px;
  border-radius: var(--radius-full);
  background: linear-gradient(135deg, var(--primary), var(--neon-pink));
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-neon);
  border: none;
  font-size: 24px;
  z-index: 100;
  text-decoration: none;
  transition: all 0.3s ease;
  animation: bounce 2s infinite;
}

.create-feast-fab:hover {
  transform: scale(1.1);
  box-shadow: 0 0 25px rgba(140, 82, 255, 0.6);
}

.create-feast-fab:active {
  transform: scale(0.95);
}

/* Chat Styles */
.chat-message {
  display: flex;
  margin-bottom: var(--space-md);
}

.chat-message.outgoing {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  margin-right: var(--space-sm);
}

.chat-message.outgoing .message-avatar {
  margin-right: 0;
  margin-left: var(--space-sm);
}

.message-content {
  max-width: 70%;
}

.message-bubble {
  background: var(--bg-card);
  padding: var(--space-md);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-xs);
}

.chat-message.outgoing .message-bubble {
  background: var(--primary);
}

.message-time {
  font-size: 12px;
  color: var(--text-tertiary);
  text-align: right;
}

.chat-input {
  flex: 1;
  padding: var(--space-md);
  background: var(--bg-card);
  border: none;
  border-radius: var(--radius-full);
  color: var(--text-primary);
  margin-right: var(--space-sm);
}

.chat-input:focus {
  outline: none;
}

/* Profile Styles */
.profile-header {
  background: linear-gradient(to right, var(--neon-blue), var(--primary));
  padding: var(--space-xl) var(--space-md) var(--space-lg);
  margin: -16px -16px 0;
  text-align: center;
  position: relative;
}

.profile-avatar {
  width: 100px;
  height: 100px;
  border-radius: var(--radius-full);
  border: 3px solid white;
  margin-bottom: var(--space-sm);
}

.profile-stats {
  display: flex;
  justify-content: space-around;
  margin: var(--space-md) 0;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
}

.stat-label {
  font-size: 12px;
  color: var(--text-tertiary);
}

.pulse {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: var(--radius-full);
  background: var(--neon-green);
  margin-right: var(--space-sm);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(0, 255, 133, 0.5);
  }
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(0, 255, 133, 0);
  }
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(0, 255, 133, 0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes logoGlow {
  0% {
    text-shadow: 0 0 10px rgba(140, 82, 255, 0.5);
  }
  100% {
    text-shadow: 0 0 20px rgba(140, 82, 255, 0.8), 0 0 30px rgba(255, 46, 147, 0.3);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Material Icons */
.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  display: inline-block;
  line-height: 1;
  text-transform: none;
  letter-spacing: normal;
  word-wrap: normal;
  white-space: nowrap;
  direction: ltr;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
}
