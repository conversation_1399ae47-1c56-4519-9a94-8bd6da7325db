<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>FeastMeet - 页面导航测试</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background: #121212;
      color: white;
      padding: 20px;
    }
    .nav-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      max-width: 1200px;
      margin: 0 auto;
    }
    .nav-item {
      background: #1E1E1E;
      padding: 15px;
      border-radius: 10px;
      text-decoration: none;
      color: white;
      border: 1px solid #333;
      transition: all 0.2s ease;
    }
    .nav-item:hover {
      background: #8C52FF;
      transform: translateY(-2px);
    }
    .nav-title {
      font-weight: bold;
      margin-bottom: 5px;
    }
    .nav-desc {
      font-size: 12px;
      color: #ccc;
    }
    h1 {
      text-align: center;
      color: #8C52FF;
      margin-bottom: 30px;
    }
  </style>
</head>
<body>
  <h1>🍽️ FeastMeet - 所有页面导航</h1>
  
  <div class="nav-grid">
    <a href="index.html#login" class="nav-item">
      <div class="nav-title">登录页</div>
      <div class="nav-desc">社交登录和邮箱登录</div>
    </a>
    
    <a href="index.html#home" class="nav-item">
      <div class="nav-title">发现页</div>
      <div class="nav-desc">浏览饭局和搜索</div>
    </a>
    
    <a href="index.html#feast-detail" class="nav-item">
      <div class="nav-title">饭局详情页</div>
      <div class="nav-desc">查看饭局完整信息</div>
    </a>
    
    <a href="index.html#create-feast" class="nav-item">
      <div class="nav-title">创建饭局页</div>
      <div class="nav-desc">发布新的饭局</div>
    </a>
    
    <a href="index.html#nearby" class="nav-item">
      <div class="nav-title">附近页</div>
      <div class="nav-desc">地图视图和附近饭局</div>
    </a>
    
    <a href="index.html#messages" class="nav-item">
      <div class="nav-title">消息页</div>
      <div class="nav-desc">聊天列表和消息</div>
    </a>
    
    <a href="index.html#chat" class="nav-item">
      <div class="nav-title">聊天页</div>
      <div class="nav-desc">单人聊天界面</div>
    </a>
    
    <a href="index.html#profile" class="nav-item">
      <div class="nav-title">个人资料页</div>
      <div class="nav-desc">用户信息和设置入口</div>
    </a>
    
    <a href="index.html#join-confirmation" class="nav-item">
      <div class="nav-title">加入确认页</div>
      <div class="nav-desc">确认参与饭局</div>
    </a>
    
    <a href="index.html#join-success" class="nav-item">
      <div class="nav-title">加入成功页</div>
      <div class="nav-desc">成功加入反馈</div>
    </a>
    
    <a href="index.html#attendees" class="nav-item">
      <div class="nav-title">参与者列表页</div>
      <div class="nav-desc">查看饭局参与者</div>
    </a>
    
    <a href="index.html#notifications" class="nav-item">
      <div class="nav-title">通知页</div>
      <div class="nav-desc">系统通知和消息</div>
    </a>
    
    <a href="index.html#settings" class="nav-item">
      <div class="nav-title">设置页</div>
      <div class="nav-desc">账户和隐私设置</div>
    </a>
    
    <a href="index.html#feast-chat" class="nav-item">
      <div class="nav-title">群聊页</div>
      <div class="nav-desc">饭局群聊界面</div>
    </a>
    
    <a href="index.html#my-feasts" class="nav-item">
      <div class="nav-title">我的饭局页</div>
      <div class="nav-desc">个人饭局管理</div>
    </a>
    
    <a href="index.html#search-results" class="nav-item">
      <div class="nav-title">搜索结果页</div>
      <div class="nav-desc">搜索结果展示</div>
    </a>
    
    <a href="index.html#filter" class="nav-item">
      <div class="nav-title">筛选页</div>
      <div class="nav-desc">高级筛选条件</div>
    </a>
  </div>
  
  <div style="text-align: center; margin-top: 40px; color: #666;">
    <p>总共 17 个页面 | 点击任意卡片进入对应页面</p>
    <p>使用浏览器的返回按钮或页面内的导航按钮进行页面切换</p>
  </div>
</body>
</html>
