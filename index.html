<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>FeastMeet - 酷酷年轻人的约饭神器</title>
  <link rel="stylesheet" href="styles.css?v=3.0">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Outfit:wght@400;500;600;700;800&display=swap" rel="stylesheet">
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">
</head>
<body>

<div class="app-container">

  <!-- 启动画面 -->
  <div class="screen" id="splash" style="display: block;">
    <div style="height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; background: linear-gradient(135deg, var(--bg-dark) 0%, var(--primary) 100%); position: relative; overflow: hidden;">
      <!-- 背景动画元素 -->
      <div style="position: absolute; width: 200px; height: 200px; background: var(--neon-pink); border-radius: 50%; top: 10%; left: -50px; opacity: 0.1; animation: float 6s ease-in-out infinite;"></div>
      <div style="position: absolute; width: 150px; height: 150px; background: var(--neon-blue); border-radius: 50%; bottom: 20%; right: -30px; opacity: 0.1; animation: float 4s ease-in-out infinite reverse;"></div>

      <!-- Logo动画 -->
      <div style="position: relative; margin-bottom: var(--space-xl);">
        <div style="font-size: 48px; font-weight: 800; text-align: center; animation: logoGlow 2s ease-in-out infinite alternate;">
          <span class="gradient-text">FeastMeet</span>
        </div>
        <div style="text-align: center; color: var(--text-secondary); margin-top: var(--space-sm); animation: fadeInUp 1s ease-out 0.5s both;">
          酷酷年轻人的约饭神器
        </div>
      </div>

      <!-- 加载动画 -->
      <div style="display: flex; gap: var(--space-xs); margin-bottom: var(--space-lg);">
        <div style="width: 8px; height: 8px; background: var(--primary); border-radius: 50%; animation: bounce 1.4s ease-in-out infinite both; animation-delay: 0s;"></div>
        <div style="width: 8px; height: 8px; background: var(--neon-pink); border-radius: 50%; animation: bounce 1.4s ease-in-out infinite both; animation-delay: 0.16s;"></div>
        <div style="width: 8px; height: 8px; background: var(--neon-blue); border-radius: 50%; animation: bounce 1.4s ease-in-out infinite both; animation-delay: 0.32s;"></div>
      </div>

      <div style="color: var(--text-tertiary); font-size: 14px; animation: fadeInUp 1s ease-out 1s both;">
        正在为你准备美食之旅...
      </div>
    </div>
  </div>

  <!-- 启动/登录页 -->
  <div class="screen" id="login">
    <div class="full-content">
      <!-- 背景装饰 -->
      <div style="position: absolute; top: 0; left: 0; right: 0; height: 40%; background: linear-gradient(135deg, var(--primary) 0%, var(--neon-pink) 100%); opacity: 0.1; border-radius: 0 0 50% 50%;"></div>

      <div style="height: 20%"></div>

      <div class="login-logo">
        <div style="position: relative;">
          <span class="gradient-text">FeastMeet</span>
          <div style="position: absolute; top: -10px; right: -20px; width: 20px; height: 20px; background: var(--neon-pink); border-radius: 50%; animation: pulse 2s infinite;"></div>
        </div>
        <div style="font-size: 16px; color: var(--text-secondary); font-weight: normal; margin-top: 8px;">酷酷年轻人的约饭神器</div>
        <div style="font-size: 14px; color: var(--text-tertiary); margin-top: 4px;">美食相遇，灵魂碰撞</div>
      </div>

      <div class="social-login">
        <a href="#home" class="social-icon" style="background: linear-gradient(45deg, #1DA1F2, #0d8bd9);">
          <span class="material-icons">alternate_email</span>
        </a>
        <a href="#home" class="social-icon" style="background: linear-gradient(45deg, #25D366, #128C7E);">
          <span class="material-icons">chat</span>
        </a>
        <a href="#home" class="social-icon" style="background: linear-gradient(45deg, #4285F4, #34A853);">
          <span class="material-icons">language</span>
        </a>
      </div>

      <div class="divider">或使用邮箱登录</div>

      <div class="form-group">
        <div style="position: relative;">
          <span class="material-icons" style="position: absolute; left: var(--space-md); top: 50%; transform: translateY(-50%); color: var(--text-tertiary); font-size: 20px;">email</span>
          <input type="email" class="form-input" placeholder="邮箱地址" style="padding-left: 50px;">
        </div>
      </div>

      <div class="form-group">
        <div style="position: relative;">
          <span class="material-icons" style="position: absolute; left: var(--space-md); top: 50%; transform: translateY(-50%); color: var(--text-tertiary); font-size: 20px;">lock</span>
          <input type="password" class="form-input" placeholder="密码" style="padding-left: 50px;">
          <button type="button" style="position: absolute; right: var(--space-md); top: 50%; transform: translateY(-50%); background: none; border: none; color: var(--text-tertiary);">
            <span class="material-icons" style="font-size: 20px;">visibility_off</span>
          </button>
        </div>
      </div>

      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--space-lg);">
        <label style="display: flex; align-items: center; font-size: 14px; color: var(--text-secondary);">
          <input type="checkbox" style="margin-right: var(--space-sm);">
          记住我
        </label>
        <a href="#" style="color: var(--primary); text-decoration: none; font-size: 14px;">忘记密码？</a>
      </div>

      <a href="#home" class="button primary full" style="margin-bottom: var(--space-md); box-shadow: 0 4px 15px rgba(140, 82, 255, 0.3);">
        <span class="button-icon material-icons">login</span>
        登录
      </a>
      <a href="#register" class="button outline full">
        <span class="button-icon material-icons">person_add</span>
        注册新账号
      </a>

      <div style="text-align: center; margin-top: var(--space-xl); color: var(--text-tertiary); font-size: 12px;">
        登录即表示您同意我们的<br>
        <a href="#" style="color: var(--primary);">服务条款</a> 和 <a href="#" style="color: var(--primary);">隐私政策</a>
      </div>
    </div>
  </div>

  <!-- 注册页 -->
  <div class="screen" id="register">
    <div class="header">
      <button class="icon-button" onclick="window.location.href='#login'">
        <span class="material-icons">arrow_back</span>
      </button>
      <div class="header-title">注册账号</div>
    </div>

    <div class="full-content">
      <div style="text-align: center; margin: var(--space-xl) 0;">
        <div style="font-size: 28px; font-weight: 700; margin-bottom: var(--space-sm);">
          <span class="gradient-text">加入FeastMeet</span>
        </div>
        <div style="color: var(--text-secondary);">开启你的美食社交之旅</div>
      </div>

      <div class="form-group">
        <label class="form-label">用户名</label>
        <input type="text" class="form-input" placeholder="设置一个酷酷的用户名">
      </div>

      <div class="form-group">
        <label class="form-label">邮箱地址</label>
        <input type="email" class="form-input" placeholder="输入你的邮箱地址">
      </div>

      <div class="form-group">
        <label class="form-label">密码</label>
        <input type="password" class="form-input" placeholder="设置登录密码 (至少8位)">
      </div>

      <div class="form-group">
        <label class="form-label">确认密码</label>
        <input type="password" class="form-input" placeholder="再次输入密码">
      </div>

      <div class="form-group">
        <label class="form-label">兴趣标签 (选择3-5个)</label>
        <div class="tags-container">
          <span class="tag">美食探索</span>
          <span class="tag">创意料理</span>
          <span class="tag">职场社交</span>
          <span class="tag">音乐爱好者</span>
          <span class="tag">电影交流</span>
          <span class="tag">艺术文化</span>
          <span class="tag">读书会</span>
          <span class="tag">创业</span>
          <span class="tag">科技</span>
          <span class="tag">运动</span>
          <span class="tag">旅行</span>
          <span class="tag">摄影</span>
        </div>
      </div>

      <div style="display: flex; align-items: center; margin: var(--space-lg) 0;">
        <input type="checkbox" id="agree-terms" style="margin-right: var(--space-sm);">
        <label for="agree-terms" style="font-size: 14px; color: var(--text-secondary);">
          我已阅读并同意 <a href="#" style="color: var(--primary);">服务条款</a> 和 <a href="#" style="color: var(--primary);">隐私政策</a>
        </label>
      </div>

      <a href="#welcome" class="button primary full" style="margin-bottom: var(--space-md);">
        <span class="button-icon material-icons">person_add</span>
        创建账号
      </a>

      <div style="text-align: center;">
        <span style="color: var(--text-tertiary);">已有账号？</span>
        <a href="#login" style="color: var(--primary); text-decoration: none; margin-left: var(--space-xs);">立即登录</a>
      </div>
    </div>
  </div>

  <!-- 欢迎页 -->
  <div class="screen" id="welcome">
    <div style="height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; padding: var(--space-xl); text-align: center;">
      <div style="width: 120px; height: 120px; background: linear-gradient(45deg, var(--neon-pink), var(--primary)); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: var(--space-xl); animation: pulse 2s infinite;">
        <span class="material-icons" style="font-size: 60px; color: white;">celebration</span>
      </div>

      <h1 style="font-size: 28px; margin-bottom: var(--space-md);">
        <span class="gradient-text">欢迎加入FeastMeet！</span>
      </h1>

      <p style="color: var(--text-secondary); margin-bottom: var(--space-xl); line-height: 1.6;">
        你的美食社交之旅即将开始！<br>
        探索附近的精彩饭局，结交志同道合的朋友，<br>
        让每一次用餐都成为难忘的体验。
      </p>

      <div style="display: flex; flex-direction: column; gap: var(--space-md); width: 100%; max-width: 280px;">
        <a href="#home" class="button primary full">
          <span class="button-icon material-icons">explore</span>
          开始探索饭局
        </a>

        <a href="#create-feast" class="button outline full">
          <span class="button-icon material-icons">add_circle</span>
          创建我的第一个饭局
        </a>
      </div>

      <div style="margin-top: var(--space-xl); color: var(--text-tertiary); font-size: 14px;">
        <span class="material-icons" style="font-size: 16px; vertical-align: middle; margin-right: var(--space-xs);">tips_and_updates</span>
        小贴士：完善个人资料可以获得更精准的饭局推荐
      </div>
    </div>
  </div>

  <!-- 主页/发现页 -->
  <div class="screen" id="home">
    <div class="header">
      <div class="header-title">探索饭局</div>
      <div class="header-action">
        <a href="#notifications" class="icon-button">
          <span class="material-icons">notifications</span>
        </a>
        <a href="#filter" class="icon-button">
          <span class="material-icons">tune</span>
        </a>
      </div>
    </div>
    
    <div class="content">
      <!-- 个性化问候 -->
      <div style="margin-bottom: var(--space-lg);">
        <div style="font-size: 24px; font-weight: 600; margin-bottom: var(--space-xs);">
          晚上好，Lisa 👋
        </div>
        <div style="color: var(--text-secondary); font-size: 14px;">
          今天有 <span style="color: var(--neon-pink);">12</span> 个精彩饭局等你发现
        </div>
      </div>

      <a href="#search-results" class="search-bar" style="text-decoration: none; color: inherit; position: relative; overflow: hidden;">
        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(90deg, transparent 0%, var(--primary) 50%, transparent 100%); opacity: 0.1; animation: shimmer 3s infinite;"></div>
        <span class="material-icons search-icon">search</span>
        <div style="flex: 1; font-size: 16px; color: var(--text-tertiary);">搜索饭局、餐厅或美食...</div>
        <span class="material-icons" style="color: var(--text-tertiary); font-size: 20px;">mic</span>
      </a>

      <div class="categories">
        <div class="category-item active">
          <span class="material-icons" style="font-size: 16px; margin-right: 4px;">recommend</span>
          推荐
        </div>
        <div class="category-item">
          <span class="material-icons" style="font-size: 16px; margin-right: 4px;">auto_awesome</span>
          新奇体验
        </div>
        <div class="category-item">
          <span class="material-icons" style="font-size: 16px; margin-right: 4px;">business</span>
          职场社交
        </div>
        <div class="category-item">
          <span class="material-icons" style="font-size: 16px; margin-right: 4px;">music_note</span>
          音乐爱好者
        </div>
        <div class="category-item">
          <span class="material-icons" style="font-size: 16px; margin-right: 4px;">movie</span>
          电影交流
        </div>
        <div class="category-item">
          <span class="material-icons" style="font-size: 16px; margin-right: 4px;">palette</span>
          艺术文化
        </div>
      </div>
      
      <a href="#feast-detail" class="feast-card">
        <div class="feast-image">
          <div class="feast-image-bg" style="background-image: url('https://images.unsplash.com/photo-1555396273-367ea4eb4db5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80');"></div>
          <div style="position: absolute; top: var(--space-md); right: var(--space-md); background: rgba(0,0,0,0.6); padding: var(--space-xs) var(--space-sm); border-radius: var(--radius-full); font-size: 12px; color: white;">
            <span class="material-icons" style="font-size: 14px; vertical-align: middle; margin-right: 2px;">person</span> 3/6
          </div>
        </div>
        <div class="feast-details">
          <h3>创意料理夜 @ 深蓝餐厅</h3>
          <div class="feast-meta">
            <div class="feast-date">
              <span class="material-icons feast-icon">event</span>
              今晚 19:30
            </div>
            <div class="feast-date">
              <span class="material-icons feast-icon">location_on</span>
              1.2km
            </div>
          </div>
          <p style="color: var(--text-secondary); margin-bottom: var(--space-sm);">分享对创意料理的热爱，探讨未来美食趋势，交流味蕾体验。</p>
          <div class="tags-container">
            <span class="tag highlighted">美食探索</span>
            <span class="tag">创意料理</span>
            <span class="tag">社交晚餐</span>
          </div>
          <div style="display: flex; align-items: center; margin-top: var(--space-md); justify-content: space-between;">
            <div style="display: flex; align-items: center;">
              <img src="https://randomuser.me/api/portraits/men/32.jpg" class="avatar small">
              <div style="margin-left: var(--space-sm); font-size: 14px;">
                <div>Alex Chen</div>
                <div style="color: var(--text-tertiary); font-size: 12px; display: flex; align-items: center;">
                  <span class="material-icons" style="font-size: 14px; margin-right: 2px;">star</span>
                  4.8
                </div>
              </div>
            </div>
            <div style="color: var(--neon-pink); font-weight: 600;">¥128/位</div>
          </div>
        </div>
      </a>

      <a href="#feast-detail" class="feast-card">
        <div class="feast-image">
          <div class="feast-image-bg" style="background-image: url('https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80');"></div>
          <div style="position: absolute; top: var(--space-md); right: var(--space-md); background: rgba(0,0,0,0.6); padding: var(--space-xs) var(--space-sm); border-radius: var(--radius-full); font-size: 12px; color: white;">
            <span class="material-icons" style="font-size: 14px; vertical-align: middle; margin-right: 2px;">person</span> 2/4
          </div>
        </div>
        <div class="feast-details">
          <h3>爵士乐与红酒之夜</h3>
          <div class="feast-meta">
            <div class="feast-date">
              <span class="material-icons feast-icon">event</span>
              明天 20:00
            </div>
            <div class="feast-date">
              <span class="material-icons feast-icon">location_on</span>
              3.5km
            </div>
          </div>
          <p style="color: var(--text-secondary); margin-bottom: var(--space-sm);">边听爵士乐边品红酒，聊聊音乐与艺术，结交志同道合的朋友。</p>
          <div class="tags-container">
            <span class="tag highlighted">音乐爱好者</span>
            <span class="tag">红酒</span>
            <span class="tag">爵士乐</span>
          </div>
          <div style="display: flex; align-items: center; margin-top: var(--space-md); justify-content: space-between;">
            <div style="display: flex; align-items: center;">
              <img src="https://randomuser.me/api/portraits/women/44.jpg" class="avatar small">
              <div style="margin-left: var(--space-sm); font-size: 14px;">
                <div>Sophia Lin</div>
                <div style="color: var(--text-tertiary); font-size: 12px; display: flex; align-items: center;">
                  <span class="material-icons" style="font-size: 14px; margin-right: 2px;">star</span>
                  4.9
                </div>
              </div>
            </div>
            <div style="color: var(--neon-pink); font-weight: 600;">¥168/位</div>
          </div>
        </div>
      </a>

      <a href="#feast-detail" class="feast-card">
        <div class="feast-image">
          <div class="feast-image-bg" style="background-image: url('https://images.unsplash.com/photo-1528605248644-14dd04022da1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80');"></div>
          <div style="position: absolute; top: var(--space-md); right: var(--space-md); background: rgba(255,46,147,0.8); padding: var(--space-xs) var(--space-sm); border-radius: var(--radius-full); font-size: 12px; color: white;">
            火爆
          </div>
        </div>
        <div class="feast-details">
          <h3>科技创业者交流会 @ 云端咖啡</h3>
          <div class="feast-meta">
            <div class="feast-date">
              <span class="material-icons feast-icon">event</span>
              周六 13:00
            </div>
            <div class="feast-date">
              <span class="material-icons feast-icon">location_on</span>
              0.8km
            </div>
          </div>
          <p style="color: var(--text-secondary); margin-bottom: var(--space-sm);">轻松氛围中交流创业心得，寻找合作伙伴，碰撞创新火花。</p>
          <div class="tags-container">
            <span class="tag highlighted">职场社交</span>
            <span class="tag">创业</span>
            <span class="tag">科技</span>
          </div>
          <div style="display: flex; align-items: center; margin-top: var(--space-md); justify-content: space-between;">
            <div style="display: flex; align-items: center;">
              <div class="avatar-group">
                <img src="https://randomuser.me/api/portraits/men/85.jpg" class="avatar small">
                <img src="https://randomuser.me/api/portraits/women/79.jpg" class="avatar small">
                <div class="users-more">+3</div>
              </div>
              <div style="margin-left: var(--space-sm); font-size: 14px;">
                <div>多人主办</div>
              </div>
            </div>
            <div style="color: var(--neon-pink); font-weight: 600;">¥88/位</div>
          </div>
        </div>
      </a>

      <a href="#create-feast" class="create-feast-fab">
        <span class="material-icons">add</span>
      </a>
    </div>
    
    <div class="nav-bar">
      <a href="#home" class="nav-item active">
        <span class="material-icons nav-icon">explore</span>
        <span>发现</span>
      </a>
      <a href="#nearby" class="nav-item">
        <span class="material-icons nav-icon">map</span>
        <span>附近</span>
      </a>
      <a href="#messages" class="nav-item">
        <span class="material-icons nav-icon">chat</span>
        <span>消息</span>
      </a>
      <a href="#profile" class="nav-item">
        <span class="material-icons nav-icon">person</span>
        <span>我的</span>
      </a>
    </div>
  </div>

  <!-- 饭局详情页 -->
  <div class="screen" id="feast-detail">
    <div class="header">
      <button class="icon-button" onclick="window.location.href='#home'">
        <span class="material-icons">arrow_back</span>
      </button>
      <div class="header-title">饭局详情</div>
      <div class="header-action">
        <button class="icon-button">
          <span class="material-icons">share</span>
        </button>
      </div>
    </div>

    <div class="full-content">
      <div style="height: 200px; position: relative; margin: -16px -16px 16px;">
        <div style="position: absolute; inset: 0; background-image: url('https://images.unsplash.com/photo-1555396273-367ea4eb4db5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80'); background-size: cover; background-position: center;"></div>
        <div style="position: absolute; inset: 0; background: linear-gradient(to top, rgba(18,18,18,1) 0%, rgba(18,18,18,0) 100%);"></div>
      </div>

      <h2 style="margin-bottom: var(--space-sm);">创意料理夜 @ 深蓝餐厅</h2>

      <div class="feast-meta" style="margin-bottom: var(--space-md);">
        <div class="feast-date">
          <span class="material-icons feast-icon">event</span>
          今晚 19:30-21:30
        </div>
        <div class="feast-date">
          <span class="material-icons feast-icon">location_on</span>
          深蓝餐厅 · 1.2km
        </div>
      </div>

      <div class="card">
        <h4 style="margin-bottom: var(--space-sm);">关于这场饭局</h4>
        <p style="color: var(--text-secondary); line-height: 1.6;">分享对创意料理的热爱，探讨未来美食趋势，交流味蕾体验。今晚的主厨特别推出分子料理新菜单，我们将共同品尝并进行有趣的美食讨论。</p>
        <p style="color: var(--text-secondary); line-height: 1.6; margin-top: var(--space-sm);">适合喜欢尝试新事物、对料理有热情的朋友。席间将有轻松的交流环节，不用担心尴尬。</p>

        <div style="margin-top: var(--space-md);">
          <div class="tags-container">
            <span class="tag highlighted">美食探索</span>
            <span class="tag">创意料理</span>
            <span class="tag">社交晚餐</span>
            <span class="tag">分子料理</span>
          </div>
        </div>
      </div>

      <div class="card">
        <h4 style="margin-bottom: var(--space-md);">主办人</h4>
        <div style="display: flex; align-items: center;">
          <img src="https://randomuser.me/api/portraits/men/32.jpg" class="avatar">
          <div style="margin-left: var(--space-md);">
            <div style="font-weight: 500;">Alex Chen</div>
            <div style="color: var(--text-tertiary); font-size: 14px; margin-top: var(--space-xs);">美食博主 | 料理爱好者</div>
            <div style="display: flex; align-items: center; margin-top: var(--space-xs);">
              <span class="material-icons" style="font-size: 16px; color: var(--neon-yellow); margin-right: 2px;">star</span>
              <span>4.8</span>
              <span style="margin-left: var(--space-xs); color: var(--text-tertiary);">(已举办32场)</span>
            </div>
          </div>
        </div>
      </div>

      <div style="margin-bottom: var(--space-xl); padding: var(--space-md) 0;">
        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-md);">
          <div>
            <div style="color: var(--text-tertiary);">人均费用</div>
            <div style="font-size: 20px; font-weight: 600; color: var(--neon-pink);">¥128</div>
          </div>
          <div>
            <div style="color: var(--text-tertiary);">剩余名额</div>
            <div style="font-size: 20px; font-weight: 600;">3位</div>
          </div>
        </div>

        <a href="#join-confirmation" class="button primary full">
          <span class="button-icon material-icons">check_circle</span>
          立即加入
        </a>
      </div>
    </div>
  </div>

  <!-- 创建饭局页 -->
  <div class="screen" id="create-feast">
    <div class="header">
      <button class="icon-button" onclick="window.location.href='#home'">
        <span class="material-icons">arrow_back</span>
      </button>
      <div class="header-title">创建新饭局</div>
    </div>

    <div class="full-content">
      <div class="form-group">
        <label class="form-label">饭局标题</label>
        <input type="text" class="form-input" placeholder="给你的饭局起个吸引人的名字">
      </div>

      <div class="form-group">
        <label class="form-label">饭局时间</label>
        <div style="display: flex; gap: var(--space-md);">
          <input type="date" class="form-input" style="flex: 1;">
          <input type="time" class="form-input" style="flex: 1;">
        </div>
      </div>

      <div class="form-group">
        <label class="form-label">选择餐厅</label>
        <div style="position: relative;">
          <input type="text" class="form-input" placeholder="搜索餐厅...">
          <span class="material-icons" style="position: absolute; right: var(--space-md); top: 50%; transform: translateY(-50%); color: var(--text-tertiary);">search</span>
        </div>
      </div>

      <div class="form-group">
        <label class="form-label">饭局描述</label>
        <textarea class="form-input" placeholder="描述一下你的饭局内容、氛围、适合什么样的人参加等..."></textarea>
      </div>

      <div class="form-group">
        <label class="form-label">人数上限</label>
        <select class="form-select">
          <option>2人</option>
          <option>4人</option>
          <option selected>6人</option>
          <option>8人</option>
          <option>10人</option>
          <option>不限</option>
        </select>
      </div>

      <div class="form-group">
        <label class="form-label">人均预算</label>
        <div style="position: relative;">
          <input type="number" class="form-input" placeholder="输入人均消费金额">
          <span style="position: absolute; left: var(--space-md); top: 50%; transform: translateY(-50%); color: var(--text-primary);">¥</span>
        </div>
      </div>

      <a href="#home" class="button primary full" style="margin: var(--space-xl) 0;">
        <span class="button-icon material-icons">celebration</span>
        发布饭局
      </a>
    </div>
  </div>

  <!-- 附近餐厅/地图页 -->
  <div class="screen" id="nearby">
    <div class="header">
      <div class="header-title">附近饭局</div>
      <div class="header-action">
        <button class="icon-button">
          <span class="material-icons">filter_list</span>
        </button>
      </div>
    </div>

    <div style="position: absolute; top: 80px; left: 0; right: 0; bottom: 80px; background-image: url('https://i.imgur.com/4N1QiVN.png'); background-size: cover; background-position: center;">
      <!-- 地图标记 -->
      <div style="position: absolute; top: 30%; left: 45%; background: var(--neon-pink); width: 16px; height: 16px; border-radius: 50%; border: 3px solid white; box-shadow: 0 0 0 2px var(--neon-pink);"></div>

      <div style="position: absolute; top: 50%; left: 30%; background: var(--primary); width: 16px; height: 16px; border-radius: 50%; border: 3px solid white; box-shadow: 0 0 0 2px var(--primary);"></div>

      <div style="position: absolute; top: 40%; left: 70%; background: var(--neon-green); width: 16px; height: 16px; border-radius: 50%; border: 3px solid white; box-shadow: 0 0 0 2px var(--neon-green);"></div>

      <!-- 当前选中地点 -->
      <div style="position: absolute; top: 65%; left: 50%; transform: translateX(-50%); background: white; border-radius: var(--radius-md); padding: var(--space-sm); width: 90%; box-shadow: var(--shadow-card);">
        <div style="display: flex;">
          <div style="width: 80px; height: 80px; border-radius: var(--radius-sm); background-image: url('https://images.unsplash.com/photo-1555396273-367ea4eb4db5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80'); background-size: cover; background-position: center;"></div>
          <div style="margin-left: var(--space-md); flex: 1; color: var(--bg-dark);">
            <div style="font-weight: 600;">创意料理夜 @ 深蓝餐厅</div>
            <div style="font-size: 14px; opacity: 0.7; margin-bottom: var(--space-xs);">今晚 19:30 · 1.2km</div>
            <div style="display: flex; gap: var(--space-xs);">
              <span style="background: var(--primary); color: white; padding: 2px 8px; border-radius: 10px; font-size: 12px;">3/6人</span>
              <span style="background: var(--neon-pink); color: white; padding: 2px 8px; border-radius: 10px; font-size: 12px;">¥128/位</span>
            </div>
          </div>
          <div style="display: flex; align-items: center;">
            <span class="material-icons" style="color: var(--bg-dark);">navigate_next</span>
          </div>
        </div>
      </div>
    </div>

    <div class="categories" style="position: absolute; top: 90px; left: var(--space-md); right: var(--space-md); z-index: 10; background: rgba(18,18,18,0.8); padding: var(--space-sm); border-radius: var(--radius-full);">
      <div class="category-item active">全部</div>
      <div class="category-item">创意料理</div>
      <div class="category-item">红酒品鉴</div>
      <div class="category-item">咖啡馆</div>
      <div class="category-item">日料</div>
    </div>

    <div class="nav-bar">
      <a href="#home" class="nav-item">
        <span class="material-icons nav-icon">explore</span>
        <span>发现</span>
      </a>
      <a href="#nearby" class="nav-item active">
        <span class="material-icons nav-icon">map</span>
        <span>附近</span>
      </a>
      <a href="#messages" class="nav-item">
        <span class="material-icons nav-icon">chat</span>
        <span>消息</span>
      </a>
      <a href="#profile" class="nav-item">
        <span class="material-icons nav-icon">person</span>
        <span>我的</span>
      </a>
    </div>
  </div>

  <!-- 消息列表页 -->
  <div class="screen" id="messages">
    <div class="header">
      <div class="header-title">消息</div>
      <div class="header-action">
        <button class="icon-button">
          <span class="material-icons">edit</span>
        </button>
      </div>
    </div>

    <div class="content">
      <div class="search-bar">
        <span class="material-icons search-icon">search</span>
        <input type="text" class="search-input" placeholder="搜索消息...">
      </div>

      <div class="categories">
        <div class="category-item active">全部</div>
        <div class="category-item">饭局</div>
        <div class="category-item">好友</div>
        <div class="category-item">系统</div>
      </div>

      <a href="#chat" class="card" style="margin-bottom: var(--space-md); padding: var(--space-md);">
        <div style="display: flex; align-items: center;">
          <div style="position: relative;">
            <img src="https://randomuser.me/api/portraits/men/32.jpg" class="avatar">
            <span style="position: absolute; bottom: 0; right: 0; width: 12px; height: 12px; background: var(--neon-green); border-radius: 50%; border: 2px solid var(--bg-card);"></span>
          </div>
          <div style="margin-left: var(--space-md); flex: 1;">
            <div style="display: flex; justify-content: space-between;">
              <div style="font-weight: 500;">Alex Chen</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">14:30</div>
            </div>
            <div style="display: flex; justify-content: space-between; margin-top: var(--space-xs);">
              <div style="color: var(--text-secondary); font-size: 14px;">期待今晚见到你！我已经准备...</div>
              <div style="background: var(--primary); color: white; font-size: 12px; height: 20px; width: 20px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">2</div>
            </div>
          </div>
        </div>
      </a>

      <a href="#feast-chat" class="card" style="margin-bottom: var(--space-md); padding: var(--space-md);">
        <div style="display: flex; align-items: center;">
          <div style="position: relative;">
            <div style="width: 50px; height: 50px; border-radius: var(--radius-md); background-image: url('https://images.unsplash.com/photo-1555396273-367ea4eb4db5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80'); background-size: cover; background-position: center;"></div>
          </div>
          <div style="margin-left: var(--space-md); flex: 1;">
            <div style="display: flex; justify-content: space-between;">
              <div style="font-weight: 500;">创意料理夜 (6人)</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">12:05</div>
            </div>
            <div style="display: flex; justify-content: space-between; margin-top: var(--space-xs);">
              <div style="color: var(--text-secondary); font-size: 14px;">Alex: 大家可以提前10分钟到餐厅...</div>
              <div style="background: var(--primary); color: white; font-size: 12px; height: 20px; width: 20px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">5</div>
            </div>
          </div>
        </div>
      </a>
    </div>

    <div class="nav-bar">
      <a href="#home" class="nav-item">
        <span class="material-icons nav-icon">explore</span>
        <span>发现</span>
      </a>
      <a href="#nearby" class="nav-item">
        <span class="material-icons nav-icon">map</span>
        <span>附近</span>
      </a>
      <a href="#messages" class="nav-item active">
        <span class="material-icons nav-icon">chat</span>
        <span>消息</span>
      </a>
      <a href="#profile" class="nav-item">
        <span class="material-icons nav-icon">person</span>
        <span>我的</span>
      </a>
    </div>
  </div>

  <!-- 聊天页面 -->
  <div class="screen" id="chat">
    <div class="header">
      <button class="icon-button" onclick="window.location.href='#messages'">
        <span class="material-icons">arrow_back</span>
      </button>
      <div class="header-title" style="display: flex; align-items: center;">
        <div>Alex Chen</div>
        <div style="width: 8px; height: 8px; background: var(--neon-green); border-radius: 50%; margin-left: var(--space-xs);"></div>
      </div>
      <div class="header-action">
        <button class="icon-button">
          <span class="material-icons">more_vert</span>
        </button>
      </div>
    </div>

    <div style="height: calc(100% - 140px); overflow-y: auto; padding: var(--space-md);">
      <div style="text-align: center; color: var(--text-tertiary); font-size: 12px; margin: var(--space-md) 0;">
        今天 14:25
      </div>

      <div class="chat-message">
        <img src="https://randomuser.me/api/portraits/men/32.jpg" class="message-avatar">
        <div class="message-content">
          <div class="message-bubble">
            嘿！很高兴你加入了今晚的创意料理夜！
          </div>
          <div class="message-time">14:25</div>
        </div>
      </div>

      <div class="chat-message outgoing">
        <img src="https://randomuser.me/api/portraits/women/33.jpg" class="message-avatar">
        <div class="message-content">
          <div class="message-bubble">
            你好Alex！我很期待今晚的活动，我对分子料理很感兴趣！
          </div>
          <div class="message-time">14:28</div>
        </div>
      </div>
    </div>

    <div style="position: absolute; bottom: 0; left: 0; right: 0; padding: var(--space-md); background: var(--bg-dark); border-top: 1px solid var(--border-light); display: flex; align-items: center;">
      <input type="text" placeholder="发送消息..." class="chat-input">
      <button class="icon-button" style="background: var(--primary);">
        <span class="material-icons" style="color: white;">send</span>
      </button>
    </div>
  </div>

  <!-- 个人资料页 -->
  <div class="screen" id="profile">
    <div class="profile-header">
      <div style="position: absolute; top: var(--space-md); right: var(--space-md);">
        <button class="icon-button" style="background: rgba(255,255,255,0.2);">
          <span class="material-icons" style="color: white;">edit</span>
        </button>
      </div>

      <div style="position: relative;">
        <img src="https://randomuser.me/api/portraits/women/33.jpg" class="profile-avatar">
        <div style="position: absolute; bottom: 0; right: 0; width: 24px; height: 24px; background: var(--neon-green); border-radius: 50%; border: 3px solid white; display: flex; align-items: center; justify-content: center;">
          <span style="width: 8px; height: 8px; background: white; border-radius: 50%;"></span>
        </div>
      </div>

      <h2 style="margin-bottom: var(--space-xs);">Lisa Wang</h2>
      <div style="color: rgba(255,255,255,0.7); margin-bottom: var(--space-sm);">美食探险家 | 摄影爱好者</div>

      <div style="display: flex; align-items: center; justify-content: center; gap: var(--space-md); margin-bottom: var(--space-sm);">
        <div style="display: flex; align-items: center;">
          <span class="material-icons" style="font-size: 16px; color: var(--neon-yellow); margin-right: 4px;">star</span>
          <span style="font-weight: 600;">4.9</span>
        </div>
        <div style="display: flex; align-items: center;">
          <span class="material-icons" style="font-size: 16px; color: var(--neon-blue); margin-right: 4px;">verified</span>
          <span style="font-size: 14px;">已认证</span>
        </div>
      </div>

      <div style="display: flex; gap: var(--space-sm);">
        <button class="button outline" style="flex: 1; padding: var(--space-sm) var(--space-md); font-size: 14px;">
          <span class="material-icons" style="font-size: 16px; margin-right: 4px;">share</span>
          分享资料
        </button>
        <button class="button outline" style="flex: 1; padding: var(--space-sm) var(--space-md); font-size: 14px;">
          <span class="material-icons" style="font-size: 16px; margin-right: 4px;">qr_code</span>
          我的二维码
        </button>
      </div>
    </div>

    <div class="content" style="padding-top: 0;">
      <div class="profile-stats">
        <div class="stat-item">
          <div class="stat-value">15</div>
          <div class="stat-label">已参与饭局</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">8</div>
          <div class="stat-label">已举办饭局</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">4.9</div>
          <div class="stat-label">平均评分</div>
        </div>
      </div>

      <div class="card">
        <h4 style="margin-bottom: var(--space-md);">个人简介</h4>
        <p style="color: var(--text-secondary); line-height: 1.6;">热爱美食和摄影的年轻人，喜欢探索城市里的隐藏美食，享受与朋友分享美好时光的感觉。希望通过饭局认识更多有趣的人！</p>
      </div>

      <div class="card">
        <h4 style="margin-bottom: var(--space-md);">兴趣标签</h4>
        <div class="tags-container">
          <span class="tag highlighted">美食探索</span>
          <span class="tag highlighted">摄影</span>
          <span class="tag">咖啡</span>
          <span class="tag">旅行</span>
          <span class="tag">电影</span>
          <span class="tag">读书</span>
        </div>
      </div>

      <div class="card">
        <h4 style="margin-bottom: var(--space-md);">快捷功能</h4>

        <a href="#my-feasts" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-md); text-decoration: none; color: inherit;">
          <div style="display: flex; align-items: center;">
            <span class="material-icons" style="margin-right: var(--space-md); color: var(--primary);">restaurant_menu</span>
            <div>
              <div>我的饭局</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">查看参与和举办的饭局</div>
            </div>
          </div>
          <span class="material-icons" style="color: var(--text-tertiary);">chevron_right</span>
        </a>

        <a href="#notifications" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-md); text-decoration: none; color: inherit;">
          <div style="display: flex; align-items: center;">
            <span class="material-icons" style="margin-right: var(--space-md); color: var(--neon-pink);">notifications</span>
            <div>
              <div>通知中心</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">查看所有通知消息</div>
            </div>
          </div>
          <div style="display: flex; align-items: center;">
            <div style="background: var(--neon-pink); color: white; font-size: 10px; height: 16px; width: 16px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: var(--space-sm);">3</div>
            <span class="material-icons" style="color: var(--text-tertiary);">chevron_right</span>
          </div>
        </a>

        <a href="#settings" style="display: flex; align-items: center; justify-content: space-between; text-decoration: none; color: inherit;">
          <div style="display: flex; align-items: center;">
            <span class="material-icons" style="margin-right: var(--space-md); color: var(--neon-blue);">settings</span>
            <div>
              <div>设置</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">账户和隐私设置</div>
            </div>
          </div>
          <span class="material-icons" style="color: var(--text-tertiary);">chevron_right</span>
        </a>
      </div>
    </div>

    <div class="nav-bar">
      <a href="#home" class="nav-item">
        <span class="material-icons nav-icon">explore</span>
        <span>发现</span>
      </a>
      <a href="#nearby" class="nav-item">
        <span class="material-icons nav-icon">map</span>
        <span>附近</span>
      </a>
      <a href="#messages" class="nav-item">
        <span class="material-icons nav-icon">chat</span>
        <span>消息</span>
      </a>
      <a href="#profile" class="nav-item active">
        <span class="material-icons nav-icon">person</span>
        <span>我的</span>
      </a>
    </div>
  </div>

  <!-- 加入饭局确认页 -->
  <div class="screen" id="join-confirmation">
    <div class="header">
      <button class="icon-button" onclick="window.location.href='#feast-detail'">
        <span class="material-icons">arrow_back</span>
      </button>
      <div class="header-title">确认加入</div>
    </div>

    <div class="full-content" style="display: flex; flex-direction: column; align-items: center; padding-top: var(--space-xl);">
      <div style="width: 100px; height: 100px; background: var(--primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: var(--space-lg); box-shadow: 0 0 20px rgba(140, 82, 255, 0.4);">
        <span class="material-icons" style="font-size: 48px; color: white;">restaurant</span>
      </div>

      <h2 style="margin-bottom: var(--space-md);">创意料理夜 @ 深蓝餐厅</h2>

      <div style="text-align: center; color: var(--text-secondary); margin-bottom: var(--space-xl); padding: 0 var(--space-lg);">
        你即将加入由 <span style="color: var(--primary);">Alex Chen</span> 主办的饭局，与其他美食爱好者一起探索创意料理！
      </div>

      <div class="card" style="width: 100%;">
        <h4 style="margin-bottom: var(--space-md);">饭局详情</h4>
        <div style="display: flex; justify-content: space-between; margin-bottom: var(--space-sm);">
          <div style="color: var(--text-tertiary);">时间</div>
          <div>今晚 19:30-21:30</div>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: var(--space-sm);">
          <div style="color: var(--text-tertiary);">地点</div>
          <div>深蓝餐厅 (1.2km)</div>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: var(--space-sm);">
          <div style="color: var(--text-tertiary);">人均费用</div>
          <div style="color: var(--neon-pink); font-weight: 600;">¥128</div>
        </div>
        <div style="display: flex; justify-content: space-between;">
          <div style="color: var(--text-tertiary);">已报名</div>
          <div>3/6人</div>
        </div>
      </div>

      <div class="form-group" style="width: 100%; margin-top: var(--space-lg);">
        <label class="form-label">留言给主办人 (选填)</label>
        <textarea class="form-input" placeholder="有什么想告诉主办人的吗？比如饮食禁忌、过敏原等..."></textarea>
      </div>

      <div style="margin-top: var(--space-xl); width: 100%;">
        <a href="#join-success" class="button primary full" style="margin-bottom: var(--space-md);">
          <span class="button-icon material-icons">check_circle</span>
          确认加入
        </a>
        <a href="#feast-detail" class="button outline full">返回</a>
      </div>
    </div>
  </div>

  <!-- 加入成功页面 -->
  <div class="screen" id="join-success">
    <div style="height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; padding: var(--space-xl);">
      <div style="width: 120px; height: 120px; background: var(--neon-green); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: var(--space-lg); box-shadow: 0 0 30px rgba(0, 255, 133, 0.4); animation: pulse 1.5s infinite;">
        <span class="material-icons" style="font-size: 60px; color: white;">check</span>
      </div>

      <h2 style="margin-bottom: var(--space-md); text-align: center;">饭局加入成功！</h2>

      <div style="text-align: center; color: var(--text-secondary); margin-bottom: var(--space-xl);">
        恭喜你成功加入「创意料理夜」！<br>已将饭局信息添加到你的日程中，我们向你发送了一条确认消息。
      </div>

      <div style="display: flex; gap: var(--space-md); width: 100%; margin-bottom: var(--space-xl);">
        <a href="#messages" class="button outline" style="flex: 1;">
          <span class="button-icon material-icons">chat</span>
          聊天群组
        </a>
        <a href="#feast-detail" class="button outline" style="flex: 1;">
          <span class="button-icon material-icons">info</span>
          饭局详情
        </a>
      </div>

      <a href="#home" class="button primary full">
        <span class="button-icon material-icons">explore</span>
        继续探索
      </a>
    </div>
  </div>

  <!-- 参与者列表页 -->
  <div class="screen" id="attendees">
    <div class="header">
      <button class="icon-button" onclick="window.location.href='#feast-detail'">
        <span class="material-icons">arrow_back</span>
      </button>
      <div class="header-title">参与者 (3/6)</div>
    </div>

    <div class="full-content">
      <div class="section-title">
        <span class="material-icons" style="margin-right: var(--space-sm); color: var(--primary);">person</span>
        主办人
      </div>

      <div class="card">
        <div style="display: flex; align-items: center;">
          <img src="https://randomuser.me/api/portraits/men/32.jpg" class="avatar">
          <div style="margin-left: var(--space-md); flex: 1;">
            <div style="font-weight: 500;">Alex Chen</div>
            <div style="color: var(--text-tertiary); font-size: 14px; margin-top: var(--space-xs);">美食博主 | 料理爱好者</div>
            <div style="display: flex; align-items: center; margin-top: var(--space-xs);">
              <span class="material-icons" style="font-size: 16px; color: var(--neon-yellow); margin-right: 2px;">star</span>
              <span>4.8</span>
              <span style="margin-left: var(--space-xs); color: var(--text-tertiary);">(已举办32场)</span>
            </div>
          </div>
          <div style="background: var(--primary); color: white; padding: var(--space-xs) var(--space-sm); border-radius: var(--radius-full); font-size: 12px;">
            主办人
          </div>
        </div>
      </div>

      <div class="section-title">
        <span class="material-icons" style="margin-right: var(--space-sm); color: var(--neon-green);">group</span>
        已报名 (3人)
      </div>

      <div class="card">
        <div style="display: flex; align-items: center; margin-bottom: var(--space-md);">
          <img src="https://randomuser.me/api/portraits/women/33.jpg" class="avatar">
          <div style="margin-left: var(--space-md); flex: 1;">
            <div style="font-weight: 500;">Lisa Wang</div>
            <div style="color: var(--text-tertiary); font-size: 14px; margin-top: var(--space-xs);">美食探险家 | 摄影爱好者</div>
            <div style="display: flex; align-items: center; margin-top: var(--space-xs);">
              <span class="material-icons" style="font-size: 16px; color: var(--neon-yellow); margin-right: 2px;">star</span>
              <span>4.9</span>
            </div>
          </div>
          <div style="color: var(--text-tertiary); font-size: 12px;">
            刚刚加入
          </div>
        </div>

        <div style="display: flex; align-items: center; margin-bottom: var(--space-md);">
          <img src="https://randomuser.me/api/portraits/men/54.jpg" class="avatar">
          <div style="margin-left: var(--space-md); flex: 1;">
            <div style="font-weight: 500;">Mike Zhang</div>
            <div style="color: var(--text-tertiary); font-size: 14px; margin-top: var(--space-xs);">料理新手 | 学习中</div>
            <div style="display: flex; align-items: center; margin-top: var(--space-xs);">
              <span class="material-icons" style="font-size: 16px; color: var(--neon-yellow); margin-right: 2px;">star</span>
              <span>4.6</span>
            </div>
          </div>
          <div style="color: var(--text-tertiary); font-size: 12px;">
            2小时前
          </div>
        </div>

        <div style="display: flex; align-items: center;">
          <img src="https://randomuser.me/api/portraits/women/68.jpg" class="avatar">
          <div style="margin-left: var(--space-md); flex: 1;">
            <div style="font-weight: 500;">Emma Liu</div>
            <div style="color: var(--text-tertiary); font-size: 14px; margin-top: var(--space-xs);">甜品师 | 烘焙达人</div>
            <div style="display: flex; align-items: center; margin-top: var(--space-xs);">
              <span class="material-icons" style="font-size: 16px; color: var(--neon-yellow); margin-right: 2px;">star</span>
              <span>4.7</span>
            </div>
          </div>
          <div style="color: var(--text-tertiary); font-size: 12px;">
            昨天
          </div>
        </div>
      </div>

      <div class="section-title">
        <span class="material-icons" style="margin-right: var(--space-sm); color: var(--text-tertiary);">add_circle_outline</span>
        剩余名额 (3人)
      </div>

      <div style="text-align: center; color: var(--text-tertiary); padding: var(--space-xl);">
        还有3个名额等待有缘人<br>
        快邀请朋友一起来吧！
      </div>

      <a href="#join-confirmation" class="button primary full">
        <span class="button-icon material-icons">person_add</span>
        我要加入
      </a>
    </div>
  </div>

  <!-- 通知页面 -->
  <div class="screen" id="notifications">
    <div class="header">
      <button class="icon-button" onclick="window.location.href='#home'">
        <span class="material-icons">arrow_back</span>
      </button>
      <div class="header-title">通知</div>
      <div class="header-action">
        <button class="icon-button">
          <span class="material-icons">done_all</span>
        </button>
      </div>
    </div>

    <div class="content">
      <div class="categories">
        <div class="category-item active">全部</div>
        <div class="category-item">饭局</div>
        <div class="category-item">系统</div>
        <div class="category-item">社交</div>
      </div>

      <div class="card" style="border-left: 3px solid var(--primary);">
        <div style="display: flex; align-items: center; margin-bottom: var(--space-sm);">
          <div style="width: 40px; height: 40px; background: var(--primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: var(--space-md);">
            <span class="material-icons" style="color: white; font-size: 20px;">restaurant</span>
          </div>
          <div style="flex: 1;">
            <div style="font-weight: 500;">饭局提醒</div>
            <div style="font-size: 12px; color: var(--text-tertiary);">5分钟前</div>
          </div>
          <div style="width: 8px; height: 8px; background: var(--primary); border-radius: 50%;"></div>
        </div>
        <p style="color: var(--text-secondary);">「创意料理夜」将在今晚19:30开始，记得准时到达深蓝餐厅哦！</p>
      </div>

      <div class="card">
        <div style="display: flex; align-items: center; margin-bottom: var(--space-sm);">
          <div style="width: 40px; height: 40px; background: var(--neon-green); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: var(--space-md);">
            <span class="material-icons" style="color: white; font-size: 20px;">person_add</span>
          </div>
          <div style="flex: 1;">
            <div style="font-weight: 500;">新成员加入</div>
            <div style="font-size: 12px; color: var(--text-tertiary);">1小时前</div>
          </div>
        </div>
        <p style="color: var(--text-secondary);">Emma Liu 加入了你的饭局「创意料理夜」，快去聊天群组打个招呼吧！</p>
      </div>

      <div class="card">
        <div style="display: flex; align-items: center; margin-bottom: var(--space-sm);">
          <div style="width: 40px; height: 40px; background: var(--neon-yellow); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: var(--space-md);">
            <span class="material-icons" style="color: var(--bg-dark); font-size: 20px;">star</span>
          </div>
          <div style="flex: 1;">
            <div style="font-weight: 500;">收到新评价</div>
            <div style="font-size: 12px; color: var(--text-tertiary);">3小时前</div>
          </div>
        </div>
        <p style="color: var(--text-secondary);">Sophia Lin 给你的饭局「爵士乐与红酒之夜」打了5星好评！</p>
      </div>
    </div>
  </div>



  <!-- 设置页面 -->
  <div class="screen" id="settings">
    <div class="header">
      <button class="icon-button" onclick="window.location.href='#profile'">
        <span class="material-icons">arrow_back</span>
      </button>
      <div class="header-title">设置</div>
    </div>

    <div class="content">
      <div class="section-title">账户设置</div>

      <div class="card">
        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-md);">
          <div style="display: flex; align-items: center;">
            <span class="material-icons" style="margin-right: var(--space-md); color: var(--primary);">person</span>
            <div>
              <div>个人资料</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">编辑个人信息和兴趣标签</div>
            </div>
          </div>
          <span class="material-icons" style="color: var(--text-tertiary);">chevron_right</span>
        </div>

        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-md);">
          <div style="display: flex; align-items: center;">
            <span class="material-icons" style="margin-right: var(--space-md); color: var(--neon-blue);">security</span>
            <div>
              <div>隐私设置</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">管理个人信息可见性</div>
            </div>
          </div>
          <span class="material-icons" style="color: var(--text-tertiary);">chevron_right</span>
        </div>

        <div style="display: flex; align-items: center; justify-content: space-between;">
          <div style="display: flex; align-items: center;">
            <span class="material-icons" style="margin-right: var(--space-md); color: var(--neon-green);">verified_user</span>
            <div>
              <div>账户安全</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">密码、验证等安全设置</div>
            </div>
          </div>
          <span class="material-icons" style="color: var(--text-tertiary);">chevron_right</span>
        </div>
      </div>

      <div class="section-title">通知设置</div>

      <div class="card">
        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-md);">
          <div style="display: flex; align-items: center;">
            <span class="material-icons" style="margin-right: var(--space-md); color: var(--primary);">notifications</span>
            <div>推送通知</div>
          </div>
          <div style="width: 40px; height: 20px; background: var(--primary); border-radius: 10px; position: relative;">
            <div style="width: 16px; height: 16px; background: white; border-radius: 50%; position: absolute; top: 2px; right: 2px;"></div>
          </div>
        </div>

        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-md);">
          <div style="display: flex; align-items: center;">
            <span class="material-icons" style="margin-right: var(--space-md); color: var(--neon-pink);">email</span>
            <div>邮件通知</div>
          </div>
          <div style="width: 40px; height: 20px; background: var(--border-light); border-radius: 10px; position: relative;">
            <div style="width: 16px; height: 16px; background: white; border-radius: 50%; position: absolute; top: 2px; left: 2px;"></div>
          </div>
        </div>

        <div style="display: flex; align-items: center; justify-content: space-between;">
          <div style="display: flex; align-items: center;">
            <span class="material-icons" style="margin-right: var(--space-md); color: var(--neon-yellow);">vibration</span>
            <div>震动提醒</div>
          </div>
          <div style="width: 40px; height: 20px; background: var(--neon-yellow); border-radius: 10px; position: relative;">
            <div style="width: 16px; height: 16px; background: white; border-radius: 50%; position: absolute; top: 2px; right: 2px;"></div>
          </div>
        </div>
      </div>

      <div class="section-title">其他</div>

      <div class="card">
        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-md);">
          <div style="display: flex; align-items: center;">
            <span class="material-icons" style="margin-right: var(--space-md); color: var(--text-tertiary);">help</span>
            <div>帮助中心</div>
          </div>
          <span class="material-icons" style="color: var(--text-tertiary);">chevron_right</span>
        </div>

        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-md);">
          <div style="display: flex; align-items: center;">
            <span class="material-icons" style="margin-right: var(--space-md); color: var(--text-tertiary);">info</span>
            <div>关于我们</div>
          </div>
          <span class="material-icons" style="color: var(--text-tertiary);">chevron_right</span>
        </div>

        <div style="display: flex; align-items: center; justify-content: space-between;">
          <div style="display: flex; align-items: center;">
            <span class="material-icons" style="margin-right: var(--space-md); color: var(--neon-pink);">logout</span>
            <div style="color: var(--neon-pink);">退出登录</div>
          </div>
          <span class="material-icons" style="color: var(--text-tertiary);">chevron_right</span>
        </div>
      </div>
    </div>
  </div>

  <!-- 群聊页面 -->
  <div class="screen" id="feast-chat">
    <div class="header">
      <button class="icon-button" onclick="window.location.href='#messages'">
        <span class="material-icons">arrow_back</span>
      </button>
      <div class="header-title" style="display: flex; align-items: center;">
        <div>
          <div>创意料理夜 (6人)</div>
          <div style="font-size: 12px; color: var(--text-tertiary); font-weight: normal;">Alex, Lisa, Mike +3</div>
        </div>
      </div>
      <div class="header-action">
        <button class="icon-button">
          <span class="material-icons">more_vert</span>
        </button>
      </div>
    </div>

    <div style="height: calc(100% - 140px); overflow-y: auto; padding: var(--space-md);">
      <div style="text-align: center; color: var(--text-tertiary); font-size: 12px; margin: var(--space-md) 0;">
        今天 12:00
      </div>

      <div class="chat-message">
        <img src="https://randomuser.me/api/portraits/men/32.jpg" class="message-avatar">
        <div class="message-content">
          <div style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 2px;">Alex Chen</div>
          <div class="message-bubble">
            大家好！欢迎加入今晚的创意料理夜！
          </div>
          <div class="message-time">12:05</div>
        </div>
      </div>

      <div class="chat-message">
        <img src="https://randomuser.me/api/portraits/men/32.jpg" class="message-avatar">
        <div class="message-content">
          <div style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 2px;">Alex Chen</div>
          <div class="message-bubble">
            今晚的主厨会为我们准备分子料理的特色菜单，大家可以提前10分钟到餐厅，我们先认识一下
          </div>
          <div class="message-time">12:06</div>
        </div>
      </div>

      <div class="chat-message outgoing">
        <img src="https://randomuser.me/api/portraits/women/33.jpg" class="message-avatar">
        <div class="message-content">
          <div style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 2px; text-align: right;">Lisa Wang</div>
          <div class="message-bubble">
            太棒了！我对分子料理很感兴趣，期待今晚的体验！
          </div>
          <div class="message-time">12:10</div>
        </div>
      </div>

      <div class="chat-message">
        <img src="https://randomuser.me/api/portraits/men/54.jpg" class="message-avatar">
        <div class="message-content">
          <div style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 2px;">Mike Zhang</div>
          <div class="message-bubble">
            我是料理新手，希望能学到很多东西！有什么需要注意的吗？
          </div>
          <div class="message-time">12:15</div>
        </div>
      </div>

      <div class="chat-message">
        <img src="https://randomuser.me/api/portraits/women/68.jpg" class="message-avatar">
        <div class="message-content">
          <div style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 2px;">Emma Liu</div>
          <div class="message-bubble">
            作为甜品师，我很好奇分子料理在甜品方面的应用！
          </div>
          <div class="message-time">12:20</div>
        </div>
      </div>
    </div>

    <div style="position: absolute; bottom: 0; left: 0; right: 0; padding: var(--space-md); background: var(--bg-dark); border-top: 1px solid var(--border-light); display: flex; align-items: center;">
      <input type="text" placeholder="发送消息..." class="chat-input">
      <button class="icon-button" style="background: var(--primary);">
        <span class="material-icons" style="color: white;">send</span>
      </button>
    </div>
  </div>

  <!-- 我的饭局页面 -->
  <div class="screen" id="my-feasts">
    <div class="header">
      <button class="icon-button" onclick="window.location.href='#profile'">
        <span class="material-icons">arrow_back</span>
      </button>
      <div class="header-title">我的饭局</div>
      <div class="header-action">
        <button class="icon-button">
          <span class="material-icons">add</span>
        </button>
      </div>
    </div>

    <div class="content">
      <div class="categories">
        <div class="category-item active">全部</div>
        <div class="category-item">已参与</div>
        <div class="category-item">已举办</div>
        <div class="category-item">历史记录</div>
      </div>

      <div class="section-title">
        <span class="material-icons" style="margin-right: var(--space-sm); color: var(--neon-green);">schedule</span>
        即将到来
      </div>

      <a href="#feast-detail" class="feast-card">
        <div class="feast-image">
          <div class="feast-image-bg" style="background-image: url('https://images.unsplash.com/photo-1555396273-367ea4eb4db5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80');"></div>
          <div style="position: absolute; top: var(--space-md); left: var(--space-md); background: var(--neon-green); padding: var(--space-xs) var(--space-sm); border-radius: var(--radius-full); font-size: 12px; color: white;">
            已参与
          </div>
        </div>
        <div class="feast-details">
          <h3>创意料理夜 @ 深蓝餐厅</h3>
          <div class="feast-meta">
            <div class="feast-date">
              <span class="material-icons feast-icon">event</span>
              今晚 19:30
            </div>
            <div class="feast-date">
              <span class="material-icons feast-icon">location_on</span>
              1.2km
            </div>
          </div>
          <div style="display: flex; align-items: center; margin-top: var(--space-md); justify-content: space-between;">
            <div style="display: flex; align-items: center;">
              <img src="https://randomuser.me/api/portraits/men/32.jpg" class="avatar small">
              <div style="margin-left: var(--space-sm); font-size: 14px;">
                <div>Alex Chen</div>
                <div style="color: var(--text-tertiary); font-size: 12px;">主办人</div>
              </div>
            </div>
            <div style="color: var(--neon-pink); font-weight: 600;">¥128/位</div>
          </div>
        </div>
      </a>

      <div class="section-title">
        <span class="material-icons" style="margin-right: var(--space-sm); color: var(--primary);">history</span>
        最近参与
      </div>

      <a href="#feast-detail" class="feast-card">
        <div class="feast-image">
          <div class="feast-image-bg" style="background-image: url('https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80');"></div>
          <div style="position: absolute; top: var(--space-md); left: var(--space-md); background: rgba(0,0,0,0.6); padding: var(--space-xs) var(--space-sm); border-radius: var(--radius-full); font-size: 12px; color: white;">
            已完成
          </div>
        </div>
        <div class="feast-details">
          <h3>爵士乐与红酒之夜</h3>
          <div class="feast-meta">
            <div class="feast-date">
              <span class="material-icons feast-icon">event</span>
              上周六 20:00
            </div>
            <div class="feast-date">
              <span class="material-icons feast-icon">star</span>
              已评价 5.0
            </div>
          </div>
          <div style="display: flex; align-items: center; margin-top: var(--space-md); justify-content: space-between;">
            <div style="display: flex; align-items: center;">
              <img src="https://randomuser.me/api/portraits/women/44.jpg" class="avatar small">
              <div style="margin-left: var(--space-sm); font-size: 14px;">
                <div>Sophia Lin</div>
                <div style="color: var(--text-tertiary); font-size: 12px;">主办人</div>
              </div>
            </div>
            <div style="color: var(--text-tertiary); font-weight: 600;">已完成</div>
          </div>
        </div>
      </a>
    </div>
  </div>

  <!-- 搜索结果页 -->
  <div class="screen" id="search-results">
    <div class="header">
      <button class="icon-button" onclick="window.location.href='#home'">
        <span class="material-icons">arrow_back</span>
      </button>
      <div class="header-title">搜索结果</div>
      <div class="header-action">
        <button class="icon-button">
          <span class="material-icons">tune</span>
        </button>
      </div>
    </div>

    <div class="content">
      <div class="search-bar">
        <span class="material-icons search-icon">search</span>
        <input type="text" class="search-input" placeholder="搜索饭局、餐厅或美食..." value="创意料理">
      </div>

      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--space-md);">
        <div style="color: var(--text-secondary);">找到 <span style="color: var(--primary);">12</span> 个相关饭局</div>
        <div style="display: flex; gap: var(--space-sm);">
          <button class="icon-button" style="background: var(--bg-card);">
            <span class="material-icons">sort</span>
          </button>
          <button class="icon-button" style="background: var(--bg-card);">
            <span class="material-icons">filter_list</span>
          </button>
        </div>
      </div>

      <a href="#feast-detail" class="feast-card">
        <div class="feast-image">
          <div class="feast-image-bg" style="background-image: url('https://images.unsplash.com/photo-1555396273-367ea4eb4db5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80');"></div>
          <div style="position: absolute; top: var(--space-md); right: var(--space-md); background: rgba(0,0,0,0.6); padding: var(--space-xs) var(--space-sm); border-radius: var(--radius-full); font-size: 12px; color: white;">
            <span class="material-icons" style="font-size: 14px; vertical-align: middle; margin-right: 2px;">person</span> 3/6
          </div>
        </div>
        <div class="feast-details">
          <h3>创意料理夜 @ 深蓝餐厅</h3>
          <div class="feast-meta">
            <div class="feast-date">
              <span class="material-icons feast-icon">event</span>
              今晚 19:30
            </div>
            <div class="feast-date">
              <span class="material-icons feast-icon">location_on</span>
              1.2km
            </div>
          </div>
          <p style="color: var(--text-secondary); margin-bottom: var(--space-sm);">分享对<mark style="background: var(--primary); color: white; padding: 0 2px;">创意料理</mark>的热爱，探讨未来美食趋势。</p>
          <div class="tags-container">
            <span class="tag highlighted">美食探索</span>
            <span class="tag highlighted">创意料理</span>
            <span class="tag">社交晚餐</span>
          </div>
        </div>
      </a>

      <a href="#feast-detail" class="feast-card">
        <div class="feast-image">
          <div class="feast-image-bg" style="background-image: url('https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80');"></div>
          <div style="position: absolute; top: var(--space-md); right: var(--space-md); background: rgba(0,0,0,0.6); padding: var(--space-xs) var(--space-sm); border-radius: var(--radius-full); font-size: 12px; color: white;">
            <span class="material-icons" style="font-size: 14px; vertical-align: middle; margin-right: 2px;">person</span> 1/4
          </div>
        </div>
        <div class="feast-details">
          <h3>分子料理工作坊 @ 未来厨房</h3>
          <div class="feast-meta">
            <div class="feast-date">
              <span class="material-icons feast-icon">event</span>
              明天 14:00
            </div>
            <div class="feast-date">
              <span class="material-icons feast-icon">location_on</span>
              2.8km
            </div>
          </div>
          <p style="color: var(--text-secondary); margin-bottom: var(--space-sm);">学习<mark style="background: var(--primary); color: white; padding: 0 2px;">创意料理</mark>技巧，体验分子美食的奇妙世界。</p>
          <div class="tags-container">
            <span class="tag highlighted">创意料理</span>
            <span class="tag">学习</span>
            <span class="tag">工作坊</span>
          </div>
        </div>
      </a>

      <div style="text-align: center; color: var(--text-tertiary); padding: var(--space-xl);">
        <span class="material-icons" style="font-size: 48px; margin-bottom: var(--space-md);">search_off</span>
        <div>没有更多结果了</div>
        <div style="font-size: 14px; margin-top: var(--space-sm);">试试调整搜索关键词或筛选条件</div>
      </div>
    </div>
  </div>

  <!-- 筛选页面 -->
  <div class="screen" id="filter">
    <div class="header">
      <button class="icon-button" onclick="window.location.href='#home'">
        <span class="material-icons">close</span>
      </button>
      <div class="header-title">筛选条件</div>
      <div class="header-action">
        <button class="icon-button" style="color: var(--primary);">
          <span style="font-size: 14px; font-weight: 600;">重置</span>
        </button>
      </div>
    </div>

    <div class="content">
      <div class="form-group">
        <label class="form-label">距离范围</label>
        <div style="padding: var(--space-md) 0;">
          <div style="position: relative; height: 6px; background: var(--border-light); border-radius: 3px;">
            <div style="position: absolute; left: 0; width: 60%; height: 100%; background: var(--primary); border-radius: 3px;"></div>
            <div style="position: absolute; left: 60%; top: -5px; width: 16px; height: 16px; background: var(--primary); border-radius: 50%; transform: translateX(-50%);"></div>
          </div>
          <div style="display: flex; justify-content: space-between; margin-top: var(--space-sm); font-size: 12px; color: var(--text-tertiary);">
            <span>0km</span>
            <span style="color: var(--primary);">3km</span>
            <span>10km+</span>
          </div>
        </div>
      </div>

      <div class="form-group">
        <label class="form-label">价格区间</label>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--space-sm);">
          <div class="category-item">¥0-50</div>
          <div class="category-item active">¥50-150</div>
          <div class="category-item">¥150-300</div>
          <div class="category-item">¥300+</div>
        </div>
      </div>

      <div class="form-group">
        <label class="form-label">饭局时间</label>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--space-sm);">
          <div class="category-item active">今天</div>
          <div class="category-item">明天</div>
          <div class="category-item">本周</div>
          <div class="category-item">下周</div>
        </div>
      </div>

      <div class="form-group">
        <label class="form-label">饭局类型</label>
        <div class="tags-container">
          <span class="tag highlighted">美食探索</span>
          <span class="tag">创意料理</span>
          <span class="tag highlighted">职场社交</span>
          <span class="tag">音乐爱好者</span>
          <span class="tag">电影交流</span>
          <span class="tag">艺术文化</span>
          <span class="tag">读书会</span>
          <span class="tag">创业</span>
          <span class="tag">科技</span>
          <span class="tag">运动</span>
        </div>
      </div>

      <div class="form-group">
        <label class="form-label">人数规模</label>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--space-sm);">
          <div class="category-item">2-4人</div>
          <div class="category-item active">4-8人</div>
          <div class="category-item">8-15人</div>
          <div class="category-item">15人+</div>
        </div>
      </div>

      <div style="position: fixed; bottom: var(--space-md); left: var(--space-md); right: var(--space-md);">
        <a href="#search-results" class="button primary full">
          <span class="button-icon material-icons">search</span>
          查看结果 (12个饭局)
        </a>
      </div>
    </div>
  </div>

</div>

<script src="script.js?v=3.0"></script>
</body>
</html>
