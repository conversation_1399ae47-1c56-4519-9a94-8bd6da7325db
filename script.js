// FeastMeet App JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize app
    initializeApp();
    
    // Add event listeners
    addEventListeners();
});

function initializeApp() {
    // Show splash screen first
    showSplashScreen();

    // Set up navigation
    setupNavigation();

    // Initialize interactive elements
    initializeInteractiveElements();
}

function showSplashScreen() {
    // Show splash screen for 3 seconds, then go to login
    setTimeout(() => {
        showScreen('login');
    }, 3000);
}

function addEventListeners() {
    // Navigation items
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const href = this.getAttribute('href');
            if (href && href.startsWith('#')) {
                showScreen(href.substring(1));
                updateActiveNavItem(this);
            }
        });
    });
    
    // Category items
    const categoryItems = document.querySelectorAll('.category-item');
    categoryItems.forEach(item => {
        item.addEventListener('click', function() {
            // Remove active class from all category items in the same container
            const container = this.closest('.categories');
            container.querySelectorAll('.category-item').forEach(cat => {
                cat.classList.remove('active');
            });
            // Add active class to clicked item
            this.classList.add('active');
        });
    });
    
    // Tag selection in create feast form
    const tags = document.querySelectorAll('.tag');
    tags.forEach(tag => {
        tag.addEventListener('click', function() {
            // Toggle highlighted class for tag selection
            if (this.closest('#create-feast')) {
                this.classList.toggle('highlighted');
                
                // Limit to 3 selected tags
                const selectedTags = this.closest('.tags-container').querySelectorAll('.tag.highlighted');
                if (selectedTags.length > 3) {
                    this.classList.remove('highlighted');
                    showToast('最多只能选择3个标签');
                }
            }
        });
    });
    
    // Form inputs focus effects
    const formInputs = document.querySelectorAll('.form-input, .search-input, .chat-input');
    formInputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
    
    // Button click effects
    const buttons = document.querySelectorAll('.button, .icon-button, .feast-card, .card');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Add click animation
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });
    
    // Chat send buttons (both individual and group chat)
    const chatInputs = document.querySelectorAll('.chat-input');
    const sendButtons = document.querySelectorAll('.icon-button[style*="primary"]');

    chatInputs.forEach((chatInput, index) => {
        const sendButton = sendButtons[index];
        if (sendButton) {
            sendButton.addEventListener('click', function() {
                sendMessage(chatInput);
            });
        }

        chatInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage(chatInput);
            }
        });
    });

    // Settings toggles
    const toggles = document.querySelectorAll('#settings [style*="border-radius: 10px"]');
    toggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            toggleSetting(this);
        });
    });

    // Notification click handlers
    const notificationCards = document.querySelectorAll('#notifications .card');
    notificationCards.forEach(card => {
        card.addEventListener('click', function() {
            // Mark as read and navigate
            this.style.opacity = '0.7';
            const notificationDot = this.querySelector('[style*="background: var(--primary); border-radius: 50%"]');
            if (notificationDot) {
                notificationDot.style.display = 'none';
            }
        });
    });
}

function setupNavigation() {
    // Handle hash changes for navigation
    window.addEventListener('hashchange', function() {
        const hash = window.location.hash.substring(1);
        if (hash) {
            showScreen(hash);
        }
    });
    
    // Handle initial hash
    const initialHash = window.location.hash.substring(1);
    if (initialHash) {
        showScreen(initialHash);
    }
}

function showScreen(screenId) {
    // Hide all screens
    const screens = document.querySelectorAll('.screen');
    screens.forEach(screen => {
        screen.style.display = 'none';
    });
    
    // Show target screen
    const targetScreen = document.getElementById(screenId);
    if (targetScreen) {
        targetScreen.style.display = 'block';
        
        // Update URL hash
        if (window.location.hash !== '#' + screenId) {
            window.location.hash = '#' + screenId;
        }
        
        // Update navigation active state
        updateNavigationForScreen(screenId);
    }
}

function updateActiveNavItem(activeItem) {
    // Remove active class from all nav items
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.classList.remove('active');
    });
    
    // Add active class to clicked item
    activeItem.classList.add('active');
}

function updateNavigationForScreen(screenId) {
    // Update nav bar active states for screens that have nav bars
    const navScreens = ['home', 'nearby', 'messages', 'profile'];
    if (navScreens.includes(screenId)) {
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.classList.remove('active');
            if (item.getAttribute('href') === '#' + screenId) {
                item.classList.add('active');
            }
        });
    }

    // Handle special screen behaviors
    handleSpecialScreenBehaviors(screenId);
}

function handleSpecialScreenBehaviors(screenId) {
    switch(screenId) {
        case 'join-success':
            // Auto redirect after 3 seconds
            setTimeout(() => {
                showScreen('home');
            }, 3000);
            break;
        case 'notifications':
            // Mark notifications as read
            markNotificationsAsRead();
            break;
        case 'feast-chat':
            // Scroll to bottom of chat
            setTimeout(() => {
                const chatContainer = document.querySelector('#feast-chat [style*="overflow-y: auto"]');
                if (chatContainer) {
                    chatContainer.scrollTop = chatContainer.scrollHeight;
                }
            }, 100);
            break;
        case 'chat':
            // Scroll to bottom of chat
            setTimeout(() => {
                const chatContainer = document.querySelector('#chat [style*="overflow-y: auto"]');
                if (chatContainer) {
                    chatContainer.scrollTop = chatContainer.scrollHeight;
                }
            }, 100);
            break;
    }
}

function markNotificationsAsRead() {
    // Remove notification badges
    const badges = document.querySelectorAll('.nav-item [style*="background: var(--neon-pink)"]');
    badges.forEach(badge => {
        badge.style.display = 'none';
    });

    // Remove notification dots
    const dots = document.querySelectorAll('[style*="background: var(--primary); border-radius: 50%"][style*="width: 8px"]');
    dots.forEach(dot => {
        dot.style.display = 'none';
    });
}

function initializeInteractiveElements() {
    // Add hover effects to interactive elements
    const interactiveElements = document.querySelectorAll('.feast-card, .card, .button, .icon-button');
    interactiveElements.forEach(element => {
        element.addEventListener('mouseenter', function() {
            this.style.transition = 'all 0.2s ease';
        });
    });

    // Initialize search functionality
    const searchInputs = document.querySelectorAll('.search-input');
    searchInputs.forEach(input => {
        input.addEventListener('input', function() {
            // Placeholder for search functionality
            console.log('Searching for:', this.value);
        });
    });

    // Add stagger animation to feast cards
    const feastCards = document.querySelectorAll('.feast-card');
    feastCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });

    // Add parallax effect to feast card images
    feastCards.forEach(card => {
        card.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            const rotateX = (y - centerY) / 10;
            const rotateY = (centerX - x) / 10;

            const image = this.querySelector('.feast-image-bg');
            if (image) {
                image.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale(1.05)`;
            }
        });

        card.addEventListener('mouseleave', function() {
            const image = this.querySelector('.feast-image-bg');
            if (image) {
                image.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1)';
            }
        });
    });

    // Add typing effect to greeting
    const greeting = document.querySelector('#home .content > div:first-child');
    if (greeting) {
        const text = greeting.querySelector('div:first-child');
        if (text) {
            const originalText = text.textContent;
            text.textContent = '';
            let i = 0;
            const typeWriter = () => {
                if (i < originalText.length) {
                    text.textContent += originalText.charAt(i);
                    i++;
                    setTimeout(typeWriter, 100);
                }
            };
            setTimeout(typeWriter, 500);
        }
    }
}

function sendMessage(chatInput) {
    if (!chatInput) {
        chatInput = document.querySelector('.chat-input');
    }

    if (chatInput && chatInput.value.trim()) {
        const message = chatInput.value.trim();

        // Add message to chat (placeholder)
        console.log('Sending message:', message);

        // Create and add message bubble
        addMessageToChat(message, chatInput);

        // Clear input
        chatInput.value = '';

        // Show toast
        showToast('消息已发送');
    }
}

function addMessageToChat(message, chatInput) {
    const chatContainer = chatInput.closest('.screen').querySelector('[style*="overflow-y: auto"]');
    if (chatContainer) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'chat-message outgoing';
        messageDiv.innerHTML = `
            <img src="https://randomuser.me/api/portraits/women/33.jpg" class="message-avatar">
            <div class="message-content">
                <div class="message-bubble">${message}</div>
                <div class="message-time">${formatTime(new Date())}</div>
            </div>
        `;
        chatContainer.appendChild(messageDiv);
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }
}

function toggleSetting(toggle) {
    const isActive = toggle.style.background.includes('var(--primary)') ||
                    toggle.style.background.includes('var(--neon-yellow)');

    if (isActive) {
        // Turn off
        toggle.style.background = 'var(--border-light)';
        const dot = toggle.querySelector('div');
        if (dot) {
            dot.style.left = '2px';
            dot.style.right = 'auto';
        }
    } else {
        // Turn on
        toggle.style.background = 'var(--primary)';
        const dot = toggle.querySelector('div');
        if (dot) {
            dot.style.right = '2px';
            dot.style.left = 'auto';
        }
    }

    showToast('设置已更新');
}

function showToast(message) {
    // Create toast element
    const toast = document.createElement('div');
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: var(--bg-card);
        color: var(--text-primary);
        padding: 12px 24px;
        border-radius: 25px;
        z-index: 1000;
        font-size: 14px;
        box-shadow: var(--shadow-card);
        border: 1px solid var(--border-light);
    `;
    
    document.body.appendChild(toast);
    
    // Remove toast after 3 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

// Utility functions
function formatTime(date) {
    return date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
    });
}

function formatDate(date) {
    return date.toLocaleDateString('zh-CN', { 
        month: 'long', 
        day: 'numeric' 
    });
}

// Export functions for potential module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        showScreen,
        showToast,
        formatTime,
        formatDate
    };
}
