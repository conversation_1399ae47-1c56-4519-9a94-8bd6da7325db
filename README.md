# FeastMeet - 酷酷年轻人的约饭神器

FeastMeet 是一个现代化的社交约饭应用，专为年轻人设计，帮助用户发现有趣的饭局、结交志同道合的朋友。

## 🌟 特性

- **🎨 现代化设计**: 采用暗色主题和霓虹色彩，符合年轻人审美
- **📱 移动端优化**: 完全响应式设计，完美适配手机屏幕
- **🍽️ 饭局发现**: 浏览附近的有趣饭局，按兴趣分类筛选
- **📍 地图导航**: 查看附近饭局的地理位置
- **💬 实时聊天**: 与饭局参与者实时交流
- **👤 个人资料**: 展示个人兴趣和饭局历史
- **🎯 智能推荐**: 基于兴趣标签的个性化推荐

## 🚀 技术栈

- **前端**: HTML5, CSS3, JavaScript (ES6+)
- **样式**: CSS Variables, Flexbox, Grid
- **图标**: Material Icons
- **字体**: Google Fonts (Outfit)
- **图片**: Unsplash API

## 📱 页面结构

### 主要页面

1. **登录页** (`#login`)
   - 社交登录选项
   - 邮箱登录表单
   - 注册入口

2. **发现页** (`#home`)
   - 搜索功能
   - 分类筛选
   - 饭局卡片列表
   - 创建饭局按钮

3. **饭局详情页** (`#feast-detail`)
   - 饭局完整信息
   - 主办人介绍
   - 参与者列表
   - 餐厅信息
   - 加入按钮

4. **创建饭局页** (`#create-feast`)
   - 饭局信息表单
   - 标签选择
   - 图片上传
   - 隐私设置

5. **附近页** (`#nearby`)
   - 地图视图
   - 地理位置标记
   - 分类筛选

6. **消息页** (`#messages`)
   - 聊天列表
   - 消息搜索
   - 分类筛选

7. **聊天页** (`#chat`)
   - 实时聊天界面
   - 消息气泡
   - 发送功能

8. **个人资料页** (`#profile`)
   - 用户信息
   - 统计数据
   - 兴趣标签
   - 快捷功能入口

### 扩展页面

9. **加入确认页** (`#join-confirmation`)
   - 饭局详情确认
   - 留言功能
   - 确认加入按钮

10. **加入成功页** (`#join-success`)
    - 成功提示动画
    - 快捷操作按钮
    - 自动跳转

11. **参与者列表页** (`#attendees`)
    - 主办人信息
    - 已报名用户
    - 剩余名额显示

12. **通知页面** (`#notifications`)
    - 分类通知列表
    - 未读消息标记
    - 一键已读功能

13. **设置页面** (`#settings`)
    - 账户设置
    - 通知设置
    - 隐私控制
    - 开关切换

14. **群聊页面** (`#feast-chat`)
    - 多人群聊
    - 成员信息显示
    - 实时消息

15. **我的饭局页** (`#my-feasts`)
    - 参与历史
    - 举办记录
    - 状态分类

16. **搜索结果页** (`#search-results`)
    - 关键词高亮
    - 结果统计
    - 排序筛选

17. **筛选页面** (`#filter`)
    - 距离范围
    - 价格区间
    - 时间选择
    - 类型标签
    - 人数规模

## 🎨 设计系统

### 颜色方案

```css
/* 主色调 */
--primary: #8C52FF;        /* 主紫色 */
--primary-dark: #6E3AD9;   /* 深紫色 */
--primary-light: #A875FF;  /* 浅紫色 */

/* 霓虹色调 */
--neon-pink: #FF2E93;      /* 霓虹粉 */
--neon-blue: #00E9FF;      /* 霓虹蓝 */
--neon-green: #00FF85;     /* 霓虹绿 */
--neon-yellow: #FFDE59;    /* 霓虹黄 */

/* 背景色 */
--bg-dark: #121212;        /* 主背景 */
--bg-card: #1E1E1E;        /* 卡片背景 */
--bg-card-hover: #252525;  /* 卡片悬停 */
```

### 间距系统

```css
--space-xs: 4px;   /* 极小间距 */
--space-sm: 8px;   /* 小间距 */
--space-md: 16px;  /* 中等间距 */
--space-lg: 24px;  /* 大间距 */
--space-xl: 32px;  /* 超大间距 */
```

### 圆角系统

```css
--radius-sm: 8px;    /* 小圆角 */
--radius-md: 12px;   /* 中等圆角 */
--radius-lg: 20px;   /* 大圆角 */
--radius-full: 9999px; /* 完全圆角 */
```

## 🛠️ 安装和运行

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd feastmeet
   ```

2. **直接运行**
   ```bash
   # 使用任何 HTTP 服务器
   python -m http.server 8000
   # 或
   npx serve .
   # 或直接在浏览器中打开 index.html
   ```

3. **访问应用**
   ```
   http://localhost:8000
   ```

## 📂 项目结构

```
feastmeet/
├── index.html          # 主HTML文件
├── styles.css          # 样式文件
├── script.js           # JavaScript逻辑
├── README.md           # 项目文档
└── Ruel.md            # 原始设计文档
```

## 🔧 功能特性

### 导航系统
- 基于 URL Hash 的单页应用导航
- 底部导航栏，支持四个主要页面切换
- 返回按钮和面包屑导航

### 交互功能
- 分类筛选（可点击切换）
- 标签选择（创建饭局时最多选择3个）
- 搜索功能（实时输入）
- 聊天发送（支持回车键）
- 按钮点击动画效果

### 响应式设计
- 移动端优先设计
- 360px 宽度的手机屏幕模拟
- 触摸友好的交互元素

## 🎯 使用说明

1. **浏览饭局**: 在发现页查看推荐的饭局
2. **筛选内容**: 使用分类标签筛选感兴趣的饭局
3. **查看详情**: 点击饭局卡片查看详细信息
4. **创建饭局**: 点击右下角的 + 按钮创建新饭局
5. **地图查看**: 在附近页面查看地理位置
6. **消息交流**: 在消息页面与其他用户聊天
7. **个人资料**: 在我的页面管理个人信息

## 🚧 开发计划

- [ ] 后端 API 集成
- [ ] 用户认证系统
- [ ] 实时聊天功能
- [ ] 地图 API 集成
- [ ] 图片上传功能
- [ ] 推送通知
- [ ] 支付集成
- [ ] 评价系统

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

---

**FeastMeet** - 让美食相遇，让灵魂碰撞 ✨
